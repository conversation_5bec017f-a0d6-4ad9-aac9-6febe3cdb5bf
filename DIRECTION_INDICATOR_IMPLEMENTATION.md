# 人物方向指示器实现总结

## 功能概述
为位置元素人物添加了方向指示器功能，包含：
1. 黄色圆形底座（在XY平面上）
2. 黄色三角箭头（在XY平面上）
3. 箭头朝向与人物的眼睛鼻子朝向一致（对应yaw值）

## 实现细节

### 修改的文件
- `src/map/person/Person.js` - 主要实现文件

### 新增的组件

#### 1. 黄色圆环底座 (directionBase) - 空心设计
```javascript
const bodyRadius = 0.2 * s; // 身体圆柱体底部半径
const ringInnerRadius = bodyRadius + 0.05 * s; // 内径，与身体保持间距
const ringOuterRadius = ringInnerRadius + 0.03 * s; // 外径，形成环形
const directionBaseGeometry = new THREE.RingGeometry(ringInnerRadius, ringOuterRadius, 16);
const directionBaseMaterial = new THREE.MeshLambertMaterial({
    color: 0xffff00, // 黄色
    transparent: true,
    opacity: 0.8,
    side: THREE.DoubleSide // 双面材质，确保从各个角度都能看到
});
const directionBase = new THREE.Mesh(directionBaseGeometry, directionBaseMaterial);
directionBase.position.set(0, 0, 0.03 * s); // 稍微抬高避免Z-fighting
```

#### 2. 黄色三角箭头 (directionArrow) - 与圆环相接
```javascript
const arrowShape = new THREE.Shape();
const arrowSize = 0.15 * s; // 箭头大小
const arrowBaseY = ringOuterRadius; // 箭头底部与圆环外径相接
const arrowTipY = arrowBaseY + arrowSize; // 箭头顶点位置

// 创建指向+Y方向的三角形，底部与圆环相接
arrowShape.moveTo(0, arrowTipY); // 箭头顶点
arrowShape.lineTo(-arrowSize * 0.5, arrowBaseY); // 左下角，与圆环相接
arrowShape.lineTo(arrowSize * 0.5, arrowBaseY); // 右下角，与圆环相接
arrowShape.lineTo(0, arrowTipY); // 回到顶点

const arrowGeometry = new THREE.ShapeGeometry(arrowShape);
const arrowMaterial = new THREE.MeshLambertMaterial({
    color: 0xffff00, // 黄色
    transparent: true,
    opacity: 0.9,
    side: THREE.DoubleSide // 双面材质
});
const directionArrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
directionArrow.position.set(0, 0, 0.04 * s); // 稍微高于圆环
```

### 关键特性

#### 1. 方向一致性
- 箭头默认指向+Y方向，与人物的眼睛鼻子朝向一致
- 通过整个group的rotation.z旋转，确保方向指示器与人物同步旋转

#### 2. 视觉设计
- 黄色配色方案 (#ffff00)
- 半透明效果（圆环opacity: 0.8，箭头opacity: 0.9）
- 在XY平面上渲染，Z坐标接近0
- **空心圆环设计**：内径与身体圆柱体保持0.05单位间距，环宽0.03单位
- **箭头与圆环相接**：箭头底部精确对接圆环外径，形成连贯的视觉效果
- 双面材质渲染，确保从各个角度都能清晰看到

#### 3. 选中状态支持
- 在`bodyParts`对象中注册了新组件
- 在`restoreOriginalColor`方法中添加了颜色恢复逻辑
- 选中时会变红，取消选中时恢复黄色

#### 4. 缩放适配
- 所有尺寸都乘以缩放因子`s`，适应不同分辨率的地图

### 代码修改点

1. **createMesh()方法** - 添加方向指示器组件创建逻辑
2. **bodyParts对象** - 注册新组件以支持选中状态
3. **restoreOriginalColor()方法** - 添加方向指示器的颜色恢复逻辑

### 测试
创建了`test-direction-indicator.html`测试文件，可以：
- 测试不同角度的旋转（0°, 45°, 90°, 180°, 270°）
- 测试选中状态切换
- 验证方向指示器与人物朝向的一致性

## 使用方法

方向指示器会自动随人物创建，无需额外配置。当调用以下方法时，方向指示器会自动跟随：

```javascript
person.setRotation(rotation);        // 设置弧度
person.setRotationDegrees(degrees);  // 设置角度
person.setSelected(true/false);      // 选中状态
```

## 技术要点

1. **坐标系统**: 使用Z轴向上的坐标系统，方向指示器在XY平面上
2. **旋转机制**: 通过group整体旋转实现，确保所有组件同步
3. **材质设置**: 使用MeshLambertMaterial支持光照效果
4. **性能优化**: 使用适当的几何体分段数，平衡视觉效果和性能

## 效果展示

方向指示器效果如图所示：
- 黄色圆形底座提供清晰的位置标识
- 黄色三角箭头明确指示人物朝向
- 与人物的眼睛鼻子朝向完全一致
- 支持360度任意角度旋转
