<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Person Direction Indicator Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        #container {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            margin: 20px auto;
            background-color: white;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        .controls button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
        .info {
            text-align: center;
            margin: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Person Direction Indicator Test</h1>
    <div class="info">
        <p>测试人物方向指示器功能：黄色圆形底座 + 黄色三角箭头</p>
        <p>箭头朝向应与人物的眼睛鼻子朝向一致</p>
    </div>
    
    <div id="container"></div>
    
    <div class="controls">
        <button onclick="rotatePerson(0)">朝向北 (0°)</button>
        <button onclick="rotatePerson(90)">朝向东 (90°)</button>
        <button onclick="rotatePerson(180)">朝向南 (180°)</button>
        <button onclick="rotatePerson(270)">朝向西 (270°)</button>
        <button onclick="rotatePerson(45)">朝向东北 (45°)</button>
        <button onclick="toggleSelection()">切换选中状态</button>
    </div>
    
    <div class="info">
        <p id="rotation-info">当前朝向: 0°</p>
        <p id="selection-info">选中状态: 未选中</p>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.178.0/build/three.module.js';
        import { OrbitControls } from 'https://unpkg.com/three@0.178.0/examples/jsm/controls/OrbitControls.js';
        
        // 简化版的Person类（用于测试）
        class Person {
            constructor(id, name, position = new THREE.Vector3(0, 0, 0), scale = 1.0, rotation = 0) {
                this.id = id;
                this.name = name;
                this.type = 'person';
                this.position = position.clone();
                this.isSelected = false;
                this.rotation = rotation;
                this.scale = scale;

                this.createMesh();
                
                if (this.rotation !== 0) {
                    this.setRotation(this.rotation);
                }
            }

            createMesh() {
                const group = new THREE.Group();
                const s = this.scale;

                // 身体
                const bodyGeometry = new THREE.CylinderGeometry(0.15 * s, 0.2 * s, 0.6 * s, 8);
                const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                body.position.z = 0.3 * s;
                body.rotation.x = Math.PI / 2;
                body.castShadow = true;
                group.add(body);

                // 头部
                const headGeometry = new THREE.SphereGeometry(0.12 * s, 8, 6);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.z = 0.72 * s;
                head.castShadow = true;
                group.add(head);

                // 眼睛
                const eyeGeometry = new THREE.SphereGeometry(0.015 * s, 4, 4);
                const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

                const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
                leftEye.position.set(-0.04 * s, 0.1 * s, 0.75 * s);
                group.add(leftEye);

                const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
                rightEye.position.set(0.04 * s, 0.1 * s, 0.75 * s);
                group.add(rightEye);

                // 鼻子
                const noseGeometry = new THREE.ConeGeometry(0.01 * s, 0.03 * s, 4);
                const noseMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
                const nose = new THREE.Mesh(noseGeometry, noseMaterial);
                nose.position.set(0, 0.11 * s, 0.71 * s);
                nose.rotation.z = Math.PI / 2;
                group.add(nose);

                // 方向指示器 - 黄色圆形底座
                const directionBaseGeometry = new THREE.CircleGeometry(0.25 * s, 16);
                const directionBaseMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0xffff00,
                    transparent: true,
                    opacity: 0.8
                });
                const directionBase = new THREE.Mesh(directionBaseGeometry, directionBaseMaterial);
                directionBase.position.set(0, 0, 0.01 * s);
                directionBase.rotation.x = 0;
                group.add(directionBase);

                // 方向指示器 - 黄色三角箭头
                const arrowShape = new THREE.Shape();
                const arrowSize = 0.15 * s;
                arrowShape.moveTo(0, arrowSize);
                arrowShape.lineTo(-arrowSize * 0.6, -arrowSize * 0.5);
                arrowShape.lineTo(arrowSize * 0.6, -arrowSize * 0.5);
                arrowShape.lineTo(0, arrowSize);

                const arrowGeometry = new THREE.ShapeGeometry(arrowShape);
                const arrowMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0xffff00,
                    transparent: true,
                    opacity: 0.9
                });
                const directionArrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
                directionArrow.position.set(0, 0, 0.02 * s);
                directionArrow.rotation.x = 0;
                group.add(directionArrow);

                group.position.copy(this.position);
                group.rotation.z = this.rotation;

                this.mesh = group;
                this.bodyParts = {
                    body, head, leftEye, rightEye, nose, directionBase, directionArrow
                };
            }

            setRotation(rotation) {
                this.rotation = rotation;
                if (this.mesh) {
                    this.mesh.rotation.z = rotation;
                    this.mesh.updateMatrix();
                    this.mesh.updateMatrixWorld(true);
                }
            }

            setRotationDegrees(degrees) {
                this.setRotation(degrees * Math.PI / 180);
            }

            getRotationDegrees() {
                const degrees = this.rotation * 180 / Math.PI;
                return Math.round(degrees * 100) / 100;
            }

            setSelected(selected) {
                this.isSelected = selected;
                if (this.mesh) {
                    this.mesh.children.forEach(child => {
                        if (child.material) {
                            if (selected) {
                                child.material.color.setHex(0xff0000);
                                child.material.emissive.setHex(0xff0000);
                            } else {
                                this.restoreOriginalColor(child);
                                child.material.emissive.setHex(0x000000);
                            }
                        }
                    });
                }
            }

            restoreOriginalColor(child) {
                if (child === this.bodyParts.body) {
                    child.material.color.setHex(0x4a90e2);
                } else if (child === this.bodyParts.head || child === this.bodyParts.nose) {
                    child.material.color.setHex(0xffdbac);
                } else if (child === this.bodyParts.leftEye || child === this.bodyParts.rightEye) {
                    child.material.color.setHex(0x000000);
                } else if (child === this.bodyParts.directionBase || child === this.bodyParts.directionArrow) {
                    child.material.color.setHex(0xffff00);
                }
            }
        }

        // 初始化场景
        const container = document.getElementById('container');
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf0f0f0);

        const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
        camera.position.set(0, -3, 2);
        camera.lookAt(0, 0, 0);
        camera.up.set(0, 0, 1);

        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        container.appendChild(renderer.domElement);

        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // 添加地面
        const groundGeometry = new THREE.PlaneGeometry(10, 10);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.receiveShadow = true;
        scene.add(ground);

        // 创建人物
        const person = new Person('test-person', 'Test Person', new THREE.Vector3(0, 0, 0), 1.0, 0);
        scene.add(person.mesh);

        // 全局函数
        window.rotatePerson = function(degrees) {
            person.setRotationDegrees(degrees);
            document.getElementById('rotation-info').textContent = `当前朝向: ${degrees}°`;
        };

        window.toggleSelection = function() {
            person.setSelected(!person.isSelected);
            document.getElementById('selection-info').textContent = `选中状态: ${person.isSelected ? '已选中' : '未选中'}`;
        };

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();

        // 处理窗口大小变化
        window.addEventListener('resize', function() {
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        });
    </script>
</body>
</html>
