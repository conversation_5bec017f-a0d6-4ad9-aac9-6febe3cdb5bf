import * as THREE from 'three';

export class Person {
    constructor(id, name, position = new THREE.Vector3(0, 0, 0), scale = 1.0, rotation = 0) {
        this.id = id;
        this.name = name;
        this.type = 'person';
        this.position = position.clone();
        this.isSelected = false;
        this.rotation = rotation; // 人物朝向角度（弧度），0表示面向+Y方向（正面朝前）
        this.scale = scale; // 缩放比例，用于适应不同分辨率的地图

        this.createMesh();
        this.setupAnimation();

        // 在mesh创建后立即应用旋转
        if (this.rotation !== 0) {
            this.setRotation(this.rotation);
        }
    }

    createMesh() {
        // 创建人物的3D模型（简化版，使用基本几何体）
        const group = new THREE.Group();

        // 根据缩放比例调整所有尺寸
        const s = this.scale;

        // 身体（圆柱体）- 调整为真实人体比例（Z轴向上）
        const bodyGeometry = new THREE.CylinderGeometry(0.15 * s, 0.2 * s, 0.6 * s, 8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.z = 0.3 * s;  // Z轴向上
        body.rotation.x = Math.PI / 2;  // 旋转圆柱体使其沿Z轴方向
        body.castShadow = true;
        group.add(body);

        // 头部（球体）- 调整为合适大小
        const headGeometry = new THREE.SphereGeometry(0.12 * s, 8, 6);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.z = 0.72 * s;  // Z轴向上
        head.castShadow = true;
        group.add(head);

        // 添加脸部特征（眼睛和鼻子）来区分正面和背面
        // 眼睛（两个小球体）- 调整位置和大小
        const eyeGeometry = new THREE.SphereGeometry(0.015 * s, 4, 4);
        const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

        const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        leftEye.position.set(-0.04 * s, 0.1 * s, 0.75 * s); // 左眼（Z轴向上，Y轴前后）
        group.add(leftEye);

        const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
        rightEye.position.set(0.04 * s, 0.1 * s, 0.75 * s); // 右眼
        group.add(rightEye);

        // 鼻子（小圆锥体）- 调整大小
        const noseGeometry = new THREE.ConeGeometry(0.01 * s, 0.03 * s, 4);
        const noseMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
        const nose = new THREE.Mesh(noseGeometry, noseMaterial);
        nose.position.set(0, 0.11 * s, 0.71 * s); // 鼻子在脸部前方
        nose.rotation.z = Math.PI / 2; // 让鼻子朝前（绕Z轴旋转）
        group.add(nose);

        // 方向指示器 - 黄色圆环底座（空心，与身体圆柱体保持间距）
        const bodyRadius = 0.2 * s; // 身体圆柱体底部半径
        const ringInnerRadius = bodyRadius + 0.05 * s; // 内径，与身体保持间距
        const ringOuterRadius = ringInnerRadius + 0.03 * s; // 外径，形成环形
        const directionBaseGeometry = new THREE.RingGeometry(ringInnerRadius, ringOuterRadius, 16);
        const directionBaseMaterial = new THREE.MeshLambertMaterial({
            color: 0xffff00, // 黄色
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide // 双面材质，确保从各个角度都能看到
        });
        const directionBase = new THREE.Mesh(directionBaseGeometry, directionBaseMaterial);
        directionBase.position.set(0, 0, 0.03 * s); // 稍微抬高一点避免Z-fighting
        directionBase.rotation.x = 0; // 保持在XY平面上
        group.add(directionBase);

        // 方向指示器 - 黄色三角箭头（与圆环相接，指向人物朝向）
        const arrowShape = new THREE.Shape();
        const arrowSize = 0.15 * s; // 箭头大小
        const arrowBaseY = ringOuterRadius; // 箭头底部与圆环外径相接
        const arrowTipY = arrowBaseY + arrowSize; // 箭头顶点位置

        // 创建一个指向+Y方向的三角形，底部与圆环相接
        arrowShape.moveTo(0, arrowTipY); // 箭头顶点
        arrowShape.lineTo(-arrowSize * 0.5, arrowBaseY); // 左下角，与圆环相接
        arrowShape.lineTo(arrowSize * 0.5, arrowBaseY); // 右下角，与圆环相接
        arrowShape.lineTo(0, arrowTipY); // 回到顶点

        const arrowGeometry = new THREE.ShapeGeometry(arrowShape);
        const arrowMaterial = new THREE.MeshLambertMaterial({
            color: 0xffff00, // 黄色
            transparent: true,
            opacity: 0.9,
            side: THREE.DoubleSide // 双面材质
        });
        const directionArrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
        directionArrow.position.set(0, 0, 0.04 * s); // 稍微高于圆环
        directionArrow.rotation.x = 0; // 保持在XY平面上
        group.add(directionArrow);

        // 左臂 - 调整大小和位置
        const armGeometry = new THREE.CylinderGeometry(0.04 * s, 0.04 * s, 0.4 * s, 6);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });

        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.22 * s, 0, 0.4 * s);  // Z轴向上
        leftArm.rotation.x = Math.PI / 2;  // 旋转手臂使其沿Z轴方向
        leftArm.rotation.z = Math.PI / 6;
        leftArm.castShadow = true;
        group.add(leftArm);

        const rightArm = new THREE.Mesh(armGeometry, armMaterial);
        rightArm.position.set(0.22 * s, 0, 0.4 * s);  // Z轴向上
        rightArm.rotation.x = Math.PI / 2;  // 旋转手臂使其沿Z轴方向
        rightArm.rotation.z = -Math.PI / 6;
        rightArm.castShadow = true;
        group.add(rightArm);

        // // 左腿 - 调整大小和位置
        // const legGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.4, 6)
        // const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 })

        // const leftLeg = new THREE.Mesh(legGeometry, legMaterial)
        // leftLeg.position.set(-0.08, -0.2, 0)
        // leftLeg.castShadow = true
        // group.add(leftLeg)

        // const rightLeg = new THREE.Mesh(legGeometry, legMaterial)
        // rightLeg.position.set(0.08, -0.2, 0)
        // rightLeg.castShadow = true
        // group.add(rightLeg)

        // 设置位置
        group.position.copy(this.position);

        // 设置旋转（绕Z轴旋转）
        group.rotation.z = this.rotation;
        console.log(`Person ${this.name} createMesh时设置旋转: ${this.rotation} 弧度`);

        // 添加用户数据
        group.userData = {
            draggable: true,
            objectType: 'person',
            objectUuid: this.uuid || this.id, // 优先使用uuid，兼容旧的id
        };

        this.mesh = group;
        this.bodyParts = {
            body,
            head,
            leftArm,
            rightArm,
            // leftLeg,
            // rightLeg,
            leftEye,
            rightEye,
            nose,
            directionBase,
            directionArrow,
        };
    }

    setupAnimation() {
        this.animationTime = 0;
        this.walkCycle = {
            armSwing: 0,
            legSwing: 0,
        };
    }

    update(deltaTime = 0.016) {
        this.animationTime += deltaTime;

        // 更新网格位置
        if (this.mesh) {
            this.mesh.position.copy(this.position);
        }
    }



    setSelected(selected) {
        this.isSelected = selected;

        if (this.mesh) {
            this.mesh.children.forEach(child => {
                if (child.material) {
                    if (selected) {
                        // 选中时设置为红色
                        child.material.color.setHex(0xff0000);
                        child.material.emissive.setHex(0xff0000);
                    } else {
                        // 恢复原始颜色
                        this.restoreOriginalColor(child);
                        child.material.emissive.setHex(0x000000);
                    }
                }
            });
        }
    }

    // 恢复原始颜色
    restoreOriginalColor(child) {
        // 根据身体部位恢复对应的原始颜色
        if (child === this.bodyParts.body) {
            child.material.color.setHex(0x4a90e2); // 蓝色身体
        } else if (child === this.bodyParts.head || child === this.bodyParts.leftArm ||
            child === this.bodyParts.rightArm || child === this.bodyParts.nose) {
            child.material.color.setHex(0xffdbac); // 肤色
        } else if (child === this.bodyParts.leftEye || child === this.bodyParts.rightEye) {
            child.material.color.setHex(0x000000); // 黑色眼睛
        } else if (child === this.bodyParts.directionBase || child === this.bodyParts.directionArrow) {
            child.material.color.setHex(0xffff00); // 黄色方向指示器
        }
    }

    updateProperty(property, value) {
        switch (property) {
        case 'name':
            this.name = value;
            break;
        case 'rotation':
            this.setRotation(value);
            break;
        }
    }

    // 设置人物朝向（弧度）（绕Z轴旋转）
    setRotation(rotation) {
        this.rotation = rotation;
        if (this.mesh) {
            this.mesh.rotation.z = rotation;  // 绕Z轴旋转
            // 强制更新矩阵
            this.mesh.updateMatrix();
            this.mesh.updateMatrixWorld(true);
            console.log(`Person ${this.name} 旋转设置成功: ${rotation} 弧度 (${rotation * 180 / Math.PI} 度), mesh.rotation.z = ${this.mesh.rotation.z}`);
        } else {
            console.warn(`Person ${this.name} 的mesh不存在，无法设置旋转`);
        }
    }

    // 设置人物朝向（角度）
    setRotationDegrees(degrees) {
        this.setRotation(degrees * Math.PI / 180);
    }

    // 获取人物朝向角度
    getRotationDegrees() {
        const degrees = this.rotation * 180 / Math.PI;
        // 保留两位小数，避免浮点数精度问题
        return Math.round(degrees * 100) / 100;
    }

    // 获取边界框（用于碰撞检测）
    getBoundingBox() {
        const box = new THREE.Box3();
        if (this.mesh) {
            box.setFromObject(this.mesh);
        }
        return box;
    }

    // 销毁对象
    dispose() {
        if (this.mesh) {
            this.mesh.children.forEach(child => {
                if (child.geometry) child.geometry.dispose();
                if (child.material) child.material.dispose();
            });
        }
    }
}
